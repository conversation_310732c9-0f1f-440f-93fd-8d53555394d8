import React, { useRef, useCallback, useState, useEffect } from 'react';
import { Input, message } from 'antd';

const { TextArea } = Input;

/**
 * 增强的TextArea组件，支持以下功能：
 * - Ctrl+X (Mac: Cmd+X): 剪切当前行
 * - Ctrl+C (Mac: Cmd+C): 复制当前行（仅在没有选中文本时）
 * - Ctrl+V (Mac: Cmd+V): 粘贴到新行
 * - Ctrl+Z (Mac: Cmd+Z): 撤销
 * - Ctrl+Y (Mac: Cmd+Y): 重做
 * - Shift+Ctrl+Z (Mac: Shift+Cmd+Z): 重做
 * - 内置防抖处理，避免频繁触发onChange
 * - 历史记录管理，支持撤销/重做操作
 */
const EnhancedTextArea = React.forwardRef((props, ref) => {
  const { onChange, debounceDelay = 300, ...restProps } = props;
  const textAreaRef = useRef(null);
  const clipboardRef = useRef(''); // 内部剪贴板，用于存储剪切/复制的内容
  const debounceTimerRef = useRef(null);
  const isUserInputRef = useRef(false); // 标记是否为用户输入
  const historyRef = useRef([]); // 历史记录
  const historyIndexRef = useRef(-1); // 当前历史位置
  const [localValue, setLocalValue] = useState(props.value || props.defaultValue || '');

  // 检测是否为Mac系统
  const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;

  // 同步外部value变化到本地状态，但避免在用户输入时覆盖
  useEffect(() => {
    if (props.value !== undefined && !isUserInputRef.current) {
      setLocalValue(props.value);
    }
  }, [props.value]);

  // 在防抖完成后重置用户输入标记
  useEffect(() => {
    const timer = setTimeout(() => {
      isUserInputRef.current = false;
    }, debounceDelay + 50); // 稍微延迟一点确保防抖完成

    return () => clearTimeout(timer);
  }, [localValue, debounceDelay]);

  // 添加到历史记录
  const addToHistory = useCallback((value) => {
    const history = historyRef.current;
    const currentIndex = historyIndexRef.current;

    // 如果当前不在历史记录的末尾，删除后面的记录
    if (currentIndex < history.length - 1) {
      historyRef.current = history.slice(0, currentIndex + 1);
    }

    // 添加新记录
    historyRef.current.push(value);
    historyIndexRef.current = historyRef.current.length - 1;

    // 限制历史记录长度
    if (historyRef.current.length > 50) {
      historyRef.current.shift();
      historyIndexRef.current--;
    }
  }, []);

  // 初始化历史记录
  useEffect(() => {
    const initialValue = props.value || props.defaultValue || '';
    if (historyRef.current.length === 0) {
      historyRef.current = [initialValue];
      historyIndexRef.current = 0;
    }
  }, [props.value, props.defaultValue]);

  // 防抖处理函数
  const debouncedOnChange = useCallback((value) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    debounceTimerRef.current = setTimeout(() => {
      if (onChange) {
        console.log(`[EnhancedTextArea] Debounced onChange triggered with value: "${value}"`);
        // 创建一个模拟的事件对象
        const mockEvent = {
          target: {
            value: value
          }
        };
        onChange(mockEvent);
      }
    }, debounceDelay);
  }, [onChange, debounceDelay]);

  // 处理输入变化
  const handleChange = useCallback((e) => {
    const value = e.target.value;
    console.log(`[EnhancedTextArea] handleChange (immediate) with value: "${value}"`);
    isUserInputRef.current = true; // 标记为用户输入
    setLocalValue(value); // 立即更新本地状态以保证输入流畅
    addToHistory(value); // 添加到历史记录
    debouncedOnChange(value); // 防抖更新外部状态
  }, [debouncedOnChange, addToHistory]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // 获取当前行的内容和位置信息
  const getCurrentLineInfo = useCallback((textarea) => {
    const { value, selectionStart, selectionEnd } = textarea;
    
    // 找到当前行的开始位置
    let lineStart = value.lastIndexOf('\n', selectionStart - 1) + 1;
    
    // 找到当前行的结束位置
    let lineEnd = value.indexOf('\n', selectionEnd);
    if (lineEnd === -1) {
      lineEnd = value.length;
    }
    
    // 获取当前行内容
    const currentLine = value.substring(lineStart, lineEnd);
    
    return {
      lineStart,
      lineEnd,
      currentLine,
      value
    };
  }, []);

  // 复制当前行
  const copyCurrentLine = useCallback(async (textarea) => {
    const { currentLine } = getCurrentLineInfo(textarea);
    
    try {
      // 尝试使用现代剪贴板API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(currentLine);
      } else {
        // 降级到内部剪贴板
        clipboardRef.current = currentLine;
      }
      
      message.success('已复制当前行');
    } catch (error) {
      // 如果剪贴板API失败，使用内部剪贴板
      clipboardRef.current = currentLine;
      message.success('已复制当前行');
    }
  }, [getCurrentLineInfo]);

  // 剪切当前行
  const cutCurrentLine = useCallback(async (textarea) => {
    const { lineStart, lineEnd, currentLine, value } = getCurrentLineInfo(textarea);
    
    try {
      // 尝试使用现代剪贴板API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(currentLine);
      } else {
        // 降级到内部剪贴板
        clipboardRef.current = currentLine;
      }
      
      // 删除当前行（包括换行符）
      let newValue;
      if (lineEnd < value.length) {
        // 不是最后一行，删除包括后面的换行符
        newValue = value.substring(0, lineStart) + value.substring(lineEnd + 1);
      } else if (lineStart > 0) {
        // 是最后一行但不是第一行，删除包括前面的换行符
        newValue = value.substring(0, lineStart - 1) + value.substring(lineEnd);
      } else {
        // 是唯一一行，直接清空
        newValue = '';
      }
      
      // 更新本地状态和外部状态
      isUserInputRef.current = true; // 标记为用户输入
      setLocalValue(newValue);
      addToHistory(newValue);
      debouncedOnChange(newValue);

      // 设置光标位置
      const newCursorPos = Math.min(lineStart, newValue.length);
      setTimeout(() => {
        textarea.setSelectionRange(newCursorPos, newCursorPos);
      }, 0);
      
      message.success('已剪切当前行');
    } catch (error) {
      console.error('剪切失败:', error);
      message.error('剪切失败');
    }
  }, [getCurrentLineInfo, debouncedOnChange, addToHistory]);

  // 粘贴到新行
  const pasteToNewLine = useCallback(async (textarea) => {
    let textToPaste = '';
    
    try {
      // 尝试使用现代剪贴板API
      if (navigator.clipboard && navigator.clipboard.readText) {
        textToPaste = await navigator.clipboard.readText();
      } else {
        // 降级到内部剪贴板
        textToPaste = clipboardRef.current;
      }
    } catch (error) {
      // 如果剪贴板API失败，使用内部剪贴板
      textToPaste = clipboardRef.current;
    }
    
    if (!textToPaste) {
      message.warning('剪贴板为空');
      return;
    }
    
    const { value, selectionStart } = textarea;
    
    // 找到当前行的结束位置
    let lineEnd = value.indexOf('\n', selectionStart);
    if (lineEnd === -1) {
      lineEnd = value.length;
    }
    
    // 在当前行后插入新行和粘贴内容
    const newValue = value.substring(0, lineEnd) + '\n' + textToPaste + value.substring(lineEnd);

    // 更新本地状态和外部状态
    isUserInputRef.current = true; // 标记为用户输入
    setLocalValue(newValue);
    addToHistory(newValue);
    debouncedOnChange(newValue);

    // 设置光标位置到粘贴内容的末尾
    const newCursorPos = lineEnd + 1 + textToPaste.length;
    setTimeout(() => {
      textarea.setSelectionRange(newCursorPos, newCursorPos);
    }, 0);
    
    message.success('已粘贴到新行');
  }, [debouncedOnChange, addToHistory]);

  // 撤销
  const undo = useCallback(() => {
    if (historyIndexRef.current > 0) {
      historyIndexRef.current--;
      const previousValue = historyRef.current[historyIndexRef.current];
      isUserInputRef.current = true;
      setLocalValue(previousValue);
      debouncedOnChange(previousValue);
      message.success('已撤销');
    } else {
      message.warning('没有可撤销的操作');
    }
  }, [debouncedOnChange]);

  // 重做
  const redo = useCallback(() => {
    if (historyIndexRef.current < historyRef.current.length - 1) {
      historyIndexRef.current++;
      const nextValue = historyRef.current[historyIndexRef.current];
      isUserInputRef.current = true;
      setLocalValue(nextValue);
      debouncedOnChange(nextValue);
      message.success('已重做');
    } else {
      message.warning('没有可重做的操作');
    }
  }, [debouncedOnChange]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e) => {
    const isCtrlOrCmd = isMac ? e.metaKey : e.ctrlKey;

    if (isCtrlOrCmd) {
      const textarea = textAreaRef.current?.resizableTextArea?.textArea || textAreaRef.current;

      switch (e.key.toLowerCase()) {
        case 'z':
          e.preventDefault();
          if (e.shiftKey) {
            // Shift+Cmd+Z / Shift+Ctrl+Z 重做
            redo();
          } else {
            // Cmd+Z / Ctrl+Z 撤销
            undo();
          }
          break;
        case 'y':
          // Cmd+Y / Ctrl+Y 重做
          e.preventDefault();
          redo();
          break;
        case 'x':
          if (!textarea) return;
          e.preventDefault();
          cutCurrentLine(textarea);
          break;
        case 'c':
          if (!textarea) return;
          // 只有在没有选中文本时才复制当前行
          if (textarea.selectionStart === textarea.selectionEnd) {
            e.preventDefault();
            copyCurrentLine(textarea);
          }
          break;
        case 'v':
          if (!textarea) return;
          e.preventDefault();
          pasteToNewLine(textarea);
          break;
        default:
          break;
      }
    }

    // 调用原始的onKeyDown处理器
    if (restProps.onKeyDown) {
      restProps.onKeyDown(e);
    }
  }, [isMac, cutCurrentLine, copyCurrentLine, pasteToNewLine, undo, redo, restProps.onKeyDown]);

  // 合并ref
  const mergedRef = useCallback((node) => {
    textAreaRef.current = node;
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
  }, [ref]);

  return (
    <TextArea
      {...restProps}
      value={localValue}
      onChange={handleChange}
      ref={mergedRef}
      onKeyDown={handleKeyDown}
    />
  );
});

EnhancedTextArea.displayName = 'EnhancedTextArea';

export default EnhancedTextArea;
