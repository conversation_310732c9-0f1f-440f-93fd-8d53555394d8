import { apiBaseUrl } from '../utils/constant.js';

// 内容相关操作的store
export const createContentStore = (set, get) => ({
  // 内容标识符和缓存
  uuid: '', // Will store Article UUID
  // contentUuid: null, // Removed, content is part of article
  // doi: '', // Removed
  // doiCache: {}, // Removed

  // 内容相关状态
  content: [], // Array of { tag, id, style, children, ... } - This is the ORIGINAL content from the referenced Content entity
  translated: {}, // Object where keys are item.id and values are translated items - This is SPECIFIC to the Article

  // 内容标识符 setters (Moved from base.js)
  // setUuid might not be directly needed if fetchArticleForEditing sets them.
  setUuid: (uuid) => set({ uuid }), // Sets Article UUID
  // setContentUuid: (contentUuid) => set({ contentUuid }), // Removed


  // 内容相关 setters
  setContent: (newContent) => { // This sets the ORIGINAL content
    console.log('[ContentStore setContent] Called. newContent (first 3 items):', JSON.stringify(newContent?.slice(0,3))); // Original log
    const currentTranslated = get().translated || {};
    const updatedTranslated = {};

    if (Array.isArray(newContent)) {
      newContent.forEach(item => {
        if (item && typeof item.id !== 'undefined' && item.id !== null) {
          // If the item.id exists in currentTranslated, keep its value, otherwise set to undefined
          updatedTranslated[item.id] = currentTranslated.hasOwnProperty(item.id) ? currentTranslated[item.id] : undefined;
        }
      });
    }
    console.log('[ContentStore setContent] updatedTranslated (first 3 entries):', JSON.stringify(Object.entries(updatedTranslated).slice(0,3))); // Modified log
    console.log('[ContentStore setContent] Current get().translated before set (first 3 entries):', JSON.stringify(Object.entries(currentTranslated).slice(0,3))); // Original log, using currentTranslated local var
    set({ content: newContent, translated: updatedTranslated }); // Use updatedTranslated
    console.log('[ContentStore setContent] After set, get().translated (first 3 entries):', JSON.stringify(Object.entries(get().translated || {}).slice(0,3))); // Original log
  },
  setTranslated: (newTranslatedData) => { // Ensure translated is always an object
    if (typeof newTranslatedData === 'object' && newTranslatedData !== null) {
      set({ translated: newTranslatedData });
    } else {
      // console.warn("Attempted to set translated to a non-object type:", newTranslatedData);
      set({ translated: {} }); // Fallback to empty object
    }
  },

  // 初始化保存锁状态
  saveTranslationLock: Promise.resolve(),
  article: null, // Stores { uuid, title, content (original), translated } for the current article

  // 内容相关操作
  fetchArticleForEditing: async (articleUuid, signal) => { // Renamed for clarity, identifier is articleUuid, accept AbortSignal
    // If a signal is provided and already aborted, don't proceed.
    if (signal && signal.aborted) {
      return []; // Or throw an AbortError if preferred by calling code
    }

    if (!articleUuid) {
      set({ content: [], translated: {}, uuid: '', article: null }); // Reset state, removed doi and contentUuid
      return [];
    }

    try {
      const response = await fetch(`${apiBaseUrl}api/article/${articleUuid}/edit`, {
        method: 'GET',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        signal // Pass signal to fetch
      });

      if (response.ok) {
        const data = await response.json();

        if (data && Array.isArray(data.content)) { // data.content is the original text
          const fetchedOriginalContent = data.content;

          let articleTranslatedObject = {};
          if (Array.isArray(data.translated)) {
            fetchedOriginalContent.forEach((contentItem) => {
              if (contentItem && typeof contentItem.id !== 'undefined') {
                const translatedEntry = data.translated.find(tItem => tItem && tItem.id === contentItem.id);
                articleTranslatedObject[contentItem.id] = translatedEntry || undefined; // Store the item or undefined
              }
            });
          } else if (typeof data.translated === 'object' && data.translated !== null) {
            articleTranslatedObject = data.translated;
          }

          fetchedOriginalContent.forEach(item => {
            if (item && typeof item.id !== 'undefined' && !(item.id in articleTranslatedObject)) {
              articleTranslatedObject[item.id] = undefined;
            }
          });

          const newState = {
            content: fetchedOriginalContent,
            translated: articleTranslatedObject,
            uuid: data.articleUuid,
            article: {
              uuid: data.articleUuid,
              title: data.title || '',
              summary: data.summary || null, // 添加summary字段
              refs: data.refs || null, // 添加refs字段
              citation: data.citation || null, // 添加citation字段
              content: fetchedOriginalContent,
              translated: articleTranslatedObject
            }
          };
          set(newState);
          return fetchedOriginalContent;
        } else {
          set({ content: [], translated: {}, uuid: articleUuid, article: null });
          return [];
        }
      } else {
        set({ content: [], translated: {}, uuid: articleUuid, article: null });
        return [];
      }
    } catch (error) {
      // if (error.name === 'AbortError') {
      // } else {
      // }
      set({ content: [], translated: {}, uuid: articleUuid, article: null });
      return [];
    }
  },

  // 保存文章（包括标题、总结、原文和翻译）
  saveArticle: async (updates) => {
    const articleUuid = get().uuid;
    if (!articleUuid) {
      throw new Error('保存文章失败，缺少文章 UUID');
    }
    if (!updates || typeof updates !== 'object' || (updates.title === undefined && updates.summary === undefined && updates.translated === undefined && updates.content === undefined)) {
      throw new Error('保存文章失败，缺少有效的更新数据 (标题、总结、原文或翻译)');
    }

    const payload = {};
    if (updates.title !== undefined) payload.title = updates.title;
    if (updates.summary !== undefined) payload.summary = updates.summary;
    if (updates.content !== undefined) payload.content = updates.content; // Content is already in the correct format

    // Roo: 改进 translated 的处理逻辑
    if (updates.translated !== undefined) {
      if (typeof updates.translated === 'object' && updates.translated !== null) {
        // 后端期望 `translated` 是一个与 `content` 长度和顺序匹配的数组。
        // `updates.translated` 是一个以 id 为键的对象。我们需要将其转换为数组。
        const currentContent = get().content || [];
        const translatedArray = currentContent.map(originalItem => {
          if (originalItem && typeof originalItem.id !== 'undefined') {
            const translatedItem = updates.translated[String(originalItem.id)];
            // 如果找到翻译项，则使用它，否则使用 null
            return translatedItem !== undefined ? translatedItem : null;
          }
          return null; // 如果原始项无效，则对应的翻译项也为 null
        });
        payload.translated = translatedArray;
      } else {
        payload.translated = []; // 如果传入的不是对象，则发送空数组
      }
    }

    // Roo: 增加对有效载荷的检查，防止发送空更新
    let hasMeaningfulUpdate = false;
    if (payload.title !== undefined || payload.summary !== undefined || payload.content !== undefined) {
      hasMeaningfulUpdate = true;
    }
    if (payload.translated) {
      // 只有当 translated 数组中至少有一个非 null 项时，才认为它是一个有意义的更新
      if (payload.translated.some(item => item !== null)) {
        hasMeaningfulUpdate = true;
      } else {
        // 如果数组只包含 null，则从有效载荷中移除它，避免后端不必要的处理
        delete payload.translated;
      }
    }

    if (!hasMeaningfulUpdate) {
      console.log('[ContentStore saveArticle] No meaningful updates to save. Skipping backend call.');
      return get().article; // 没有需要保存的有效内容
    }

    try {
      const response = await fetch(`${apiBaseUrl}api/articles/${articleUuid}`, {
        method: 'PUT',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to parse error response from server.' }));
        throw new Error(`保存文章失败: ${errorData.message || response.statusText}`);
      }

      const updatedArticleDataFromBackend = await response.json();

      // Roo: 使用从后端返回的数据来更新状态，确保前后端一致
      set((state) => {
        const newArticleDetail = { ...state.article, ...updatedArticleDataFromBackend.article };
        let newContentState = state.content;
        let newTranslatedState = state.translated;

        // 如果 content 或 translated 被更新，则使用传入的 `updates` 对象，因为后端不返回完整的内容
        if (updates.content !== undefined) {
          newContentState = updates.content;
          newArticleDetail.content = newContentState;
        }
        if (updates.translated !== undefined) {
          newTranslatedState = updates.translated;
          newArticleDetail.translated = newTranslatedState;
        }

        const newStateToSet = {
          article: newArticleDetail,
          content: newContentState,
          translated: newTranslatedState
        };
        
        const currentUserArticles = get().userArticles || [];
        const userArticlesUpdated = currentUserArticles.some(art => art.uuid === articleUuid && art.title !== newArticleDetail.title);
        
        if (userArticlesUpdated) {
          newStateToSet.userArticles = currentUserArticles.map(art =>
            art.uuid === articleUuid ? { ...art, title: newArticleDetail.title } : art
          );
        }

        return newStateToSet;
      });

      // Roo: 返回更新后的 article 对象
      return get().article;
    } catch (error) {
      console.error('[ContentStore saveArticle] Error during saveArticle process:', error);
      throw error;
    }
  },

  clearAllTranslations: async () => {
    const { content, saveArticle, uuid } = get();
    if (!content || content.length === 0) {
      return;
    }
    const emptyTranslatedObject = {};
    content.forEach(item => {
        if (item && typeof item.id !== 'undefined') {
            emptyTranslatedObject[item.id] = undefined;
        }
    });

    set({ translated: emptyTranslatedObject });

    try {
      const currentContent = get().content;
      await saveArticle({ translated: emptyTranslatedObject, content: currentContent });
    } catch (error) {
      console.error("[ContentStore clearAllTranslations] Error clearing translations on backend:", error);
    }
  },

  updateOriginalChunk: async (originalChunkItems, newContentArrayFromEditor) => {
    const { content: currentGlobalContent, uuid: articleUuid, setContent, saveArticle } = get();


    if (Array.isArray(originalChunkItems) && originalChunkItems.length === 0 &&
        Array.isArray(currentGlobalContent) && currentGlobalContent.length === 0) {

      const newContentWithIds = newContentArrayFromEditor.map((item, index) => ({
        ...item,
        id: index
      }));

      setContent(newContentWithIds);
      const contentAfterSet = get().content;
      const translatedAfterSet = get().translated;


      if (!articleUuid) {
        throw new Error("updateOriginalChunk (Scenario 1): 无法保存原文，缺少文章标识符");
      }

      try {
        const translatedForSave = get().translated;
        await saveArticle({ content: newContentWithIds, translated: translatedForSave });
        return;
      } catch (error) {
        console.error("[ContentStore updateOriginalChunk] (Scenario 1): Error during backend save.", error);
        throw error;
      }
    }

    if (!originalChunkItems || originalChunkItems.length === 0) {
      throw new Error("updateOriginalChunk (Scenario 2): 无效的 chunkItems 或状态不一致");
    }

    let finalContentArray;
    if (originalChunkItems.length !== newContentArrayFromEditor.length) {
      const validGlobalContent = currentGlobalContent.filter(item => item !== null);
      let insertionPoint = 0;

      if (originalChunkItems.length > 0 && originalChunkItems[0].id !== undefined) {
        const firstOriginalItemIdInChunk = originalChunkItems[0].id;
        insertionPoint = validGlobalContent.findIndex(item => item.id === firstOriginalItemIdInChunk);
        if (insertionPoint === -1) {
            insertionPoint = validGlobalContent.length;
        }
      } else if (originalChunkItems.length === 0 && validGlobalContent.length > 0) {
        insertionPoint = validGlobalContent.length;
      }

      let numberOfValidOriginalItemsInChunk = 0;
      const originalItemIdsInChunk = originalChunkItems.map(item => item.id).filter(id => id !== undefined);
      if (originalItemIdsInChunk.length > 0) {
          for(let i = 0; i < originalItemIdsInChunk.length; i++) {
              if (insertionPoint + i < validGlobalContent.length && validGlobalContent[insertionPoint + i].id === originalItemIdsInChunk[i]) {
                  numberOfValidOriginalItemsInChunk++;
              } else {
                  break;
              }
          }
      }

      const contentBefore = validGlobalContent.slice(0, insertionPoint);
      const contentAfter = validGlobalContent.slice(insertionPoint + numberOfValidOriginalItemsInChunk);
      finalContentArray = [
        ...contentBefore,
        ...newContentArrayFromEditor,
        ...contentAfter
      ];

    } else {
      finalContentArray = [...currentGlobalContent];
      originalChunkItems.forEach((originalItemFromChunk, editorIndex) => {
        const originalIdToReplace = originalItemFromChunk.id;
        const newItemDataFromEditor = newContentArrayFromEditor[editorIndex];

        const targetIndexInFinalArray = finalContentArray.findIndex(item => item && item.id === originalIdToReplace);

        if (targetIndexInFinalArray !== -1) {
          finalContentArray[targetIndexInFinalArray] = { ...newItemDataFromEditor, id: originalIdToReplace };
        }
      });
    }

    const finalContentWithProperIds = finalContentArray.filter(item => item !== null).map((item, index) => ({
      ...item,
      id: index
    }));

    setContent(finalContentWithProperIds);

    const translatedAfterSetContent = { ...get().translated };


    if (!articleUuid) {
      throw new Error("updateOriginalChunk (Scenario 2): 无法保存原文，缺少文章标识符");
    }
    try {
      const translatedForSave = translatedAfterSetContent;

      await saveArticle({ content: finalContentWithProperIds, translated: translatedForSave });
    } catch (error) {
      console.error("[ContentStore updateOriginalChunk] (Scenario 2): Error during backend save.", error);
      throw error;
    }
  },

  deleteChunk: async (chunkItems) => {
    const { uuid: articleUuid, saveArticle, content: currentContent, translated: currentTranslated, setContent: setContentAction } = get();
    if (!chunkItems || chunkItems.length === 0) {
      throw new Error("deleteChunk: 无效的 chunkItems");
    }

    const itemIdsToDelete = chunkItems.map(item => item.id).filter(id => id !== undefined && id !== null);
    if (itemIdsToDelete.length === 0) {
      return;
    }

    let originalContentWasModified = false;
    const updatedOriginalContentWithNulls = currentContent.map(item => {
      if (item && typeof item.id !== 'undefined' && itemIdsToDelete.includes(item.id) && item !== null) {
        originalContentWasModified = true;
        return null;
      }
      return item;
    });

    const finalContentAfterDelete = updatedOriginalContentWithNulls.filter(item => item !== null).map((item, index) => ({
        ...item,
        id: index
    }));


    let articleTranslationWasModified = false;
    for (const id of itemIdsToDelete) {
        if (Object.prototype.hasOwnProperty.call(currentTranslated, id)) {
            articleTranslationWasModified = true;
            break;
        }
    }


    if (!originalContentWasModified && !articleTranslationWasModified && currentContent.length === finalContentAfterDelete.length) {
       return;
    }

    setContentAction(finalContentAfterDelete);


    if (!articleUuid) {
      throw new Error("deleteChunk: 无法保存删除，缺少文章标识符");
    }

    try {
      await saveArticle({ content: finalContentAfterDelete, translated: get().translated });
    } catch (error) {
      console.error("[ContentStore deleteChunk]: Error during backend save operation:", error);
      throw error;
    }
  },
}); // End of createContentStore
